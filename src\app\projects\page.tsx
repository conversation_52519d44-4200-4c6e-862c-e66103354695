'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { MainLayout } from '@/components/layout/main-layout'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Progress } from '@/components/ui/progress'
import { 
  FolderOpen, 
  Plus, 
  Calendar,
  Users,
  CheckSquare2,
  MoreHorizontal,
  Clock
} from 'lucide-react'

// Mock data for projects
const mockProjects = [
  {
    id: '1',
    name: 'TaskPro Mobile App',
    description: 'Native mobile application for iOS and Android',
    team: 'Frontend Development',
    progress: 75,
    totalTasks: 24,
    completedTasks: 18,
    dueDate: '2024-02-15',
    status: 'In Progress',
    members: [
      { id: '1', name: '<PERSON>', avatar: '' },
      { id: '2', name: '<PERSON>', avatar: '' },
      { id: '3', name: '<PERSON>', avatar: '' },
    ]
  },
  {
    id: '2',
    name: 'API v2.0',
    description: 'Complete rewrite of the backend API with new features',
    team: 'Backend Development',
    progress: 45,
    totalTasks: 32,
    completedTasks: 14,
    dueDate: '2024-03-01',
    status: 'In Progress',
    members: [
      { id: '4', name: 'Sarah Wilson', avatar: '' },
      { id: '5', name: 'Tom Brown', avatar: '' },
    ]
  },
  {
    id: '3',
    name: 'Website Redesign',
    description: 'Modern redesign of the company website',
    team: 'Design Team',
    progress: 90,
    totalTasks: 16,
    completedTasks: 14,
    dueDate: '2024-01-30',
    status: 'Review',
    members: [
      { id: '6', name: 'Emily Davis', avatar: '' },
      { id: '7', name: 'Alex Chen', avatar: '' },
    ]
  },
  {
    id: '4',
    name: 'User Analytics Dashboard',
    description: 'Analytics dashboard for tracking user behavior',
    team: 'Frontend Development',
    progress: 20,
    totalTasks: 28,
    completedTasks: 6,
    dueDate: '2024-04-15',
    status: 'Planning',
    members: [
      { id: '1', name: 'John Doe', avatar: '' },
      { id: '8', name: 'Lisa Wang', avatar: '' },
    ]
  }
]

const getStatusColor = (status: string) => {
  switch (status) {
    case 'Planning':
      return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300'
    case 'In Progress':
      return 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-300'
    case 'Review':
      return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300'
    case 'Completed':
      return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300'
    default:
      return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300'
  }
}

export default function ProjectsPage() {
  const [projects] = useState(mockProjects)
  const router = useRouter()

  const handleNewProject = () => {
    router.push('/projects/new')
  }

  const handleViewDetails = (projectId: string) => {
    router.push(`/projects/${projectId}`)
  }

  const handleViewTimeline = (projectId: string) => {
    router.push(`/projects/${projectId}/timeline`)
  }

  return (
    <MainLayout>
      <div className="space-y-6 p-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">
              <span className="gradient-text">Projects</span>
            </h1>
            <p className="text-muted-foreground">
              Manage your projects and track progress across teams
            </p>
          </div>
          <Button className="btn-primary" onClick={handleNewProject}>
            <Plus className="mr-2 h-4 w-4" />
            New Project
          </Button>
        </div>

        {/* Projects Grid */}
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-2">
          {projects.map((project) => (
            <Card key={project.id} className="card-hover border-primary/10">
              <CardHeader>
                <div className="flex items-start justify-between">
                  <div className="space-y-1 flex-1">
                    <CardTitle className="flex items-center gap-2">
                      <div className="p-1 rounded-lg bg-gradient-to-br from-primary to-blue-500 shadow-lg shadow-primary/25">
                        <FolderOpen className="h-4 w-4 text-white" />
                      </div>
                      {project.name}
                    </CardTitle>
                    <CardDescription>{project.description}</CardDescription>
                  </div>
                  <Button variant="ghost" size="icon">
                    <MoreHorizontal className="h-4 w-4" />
                  </Button>
                </div>
              </CardHeader>
              
              <CardContent className="space-y-4">
                {/* Status and Team */}
                <div className="flex items-center justify-between">
                  <Badge className={getStatusColor(project.status)}>
                    {project.status}
                  </Badge>
                  <span className="text-sm text-muted-foreground">{project.team}</span>
                </div>

                {/* Progress */}
                <div className="space-y-2">
                  <div className="flex items-center justify-between text-sm">
                    <span className="font-medium">Progress</span>
                    <span className="text-muted-foreground">{project.progress}%</span>
                  </div>
                  <Progress value={project.progress} className="h-2" />
                </div>

                {/* Stats */}
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div className="flex items-center gap-2">
                    <CheckSquare2 className="h-4 w-4 text-muted-foreground" />
                    <span>{project.completedTasks}/{project.totalTasks} tasks</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Calendar className="h-4 w-4 text-muted-foreground" />
                    <span>{new Date(project.dueDate).toLocaleDateString()}</span>
                  </div>
                </div>

                {/* Team Members */}
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">Team</span>
                    <div className="flex items-center gap-1 text-sm text-muted-foreground">
                      <Users className="h-3 w-3" />
                      {project.members.length}
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="flex -space-x-2">
                      {project.members.map((member) => (
                        <Avatar key={member.id} className="h-8 w-8 border-2 border-background">
                          <AvatarImage src={member.avatar} />
                          <AvatarFallback className="text-xs">
                            {member.name.split(' ').map(n => n[0]).join('')}
                          </AvatarFallback>
                        </Avatar>
                      ))}
                    </div>
                  </div>
                </div>

                {/* Actions */}
                <div className="flex gap-2 pt-2">
                  <Button
                    variant="outline"
                    size="sm"
                    className="flex-1"
                    onClick={() => handleViewDetails(project.id)}
                  >
                    View Details
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    className="flex-1"
                    onClick={() => handleViewTimeline(project.id)}
                  >
                    <Clock className="mr-2 h-4 w-4" />
                    Timeline
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Empty State */}
        {projects.length === 0 && (
          <Card className="text-center py-12">
            <CardContent>
              <div className="mx-auto w-12 h-12 bg-gradient-to-br from-primary to-blue-500 rounded-lg flex items-center justify-center mb-4 shadow-lg shadow-primary/25">
                <FolderOpen className="h-6 w-6 text-white" />
              </div>
              <h3 className="text-lg font-semibold mb-2">No projects yet</h3>
              <p className="text-muted-foreground mb-4">
                Create your first project to start organizing your work
              </p>
              <Button className="btn-primary" onClick={handleNewProject}>
                <Plus className="mr-2 h-4 w-4" />
                Create Your First Project
              </Button>
            </CardContent>
          </Card>
        )}
      </div>
    </MainLayout>
  )
}
